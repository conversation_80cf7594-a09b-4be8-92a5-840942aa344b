
<script id="dianping-plugin-image-gallery-template-main-index" type="text/html">
<style>
    .image-gallery-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        gap: 15px;
        padding: 10px;
    }

    .image-item {
        position: relative;
        width: 300px;
        margin-bottom: 20px;
        border: 1px solid #eee;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .image-item img {
        width: 100%;
        height: 250px;
        object-fit: contain;
        display: block;
    }

    .image-footer {
        padding: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #f9f9f9;
    }

    .image-number {
        font-size: 14px;
        color: #666;
    }

    .edit-image-btn {
        padding: 5px 10px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
    }

    .edit-image-btn:hover {
        background-color: #0056b3;
    }

    .edit-image-btn:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
        opacity: 0.6;
    }

    .image-upload-loading {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.9);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        border-radius: 4px;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 10px;
    }

    .loading-text {
        color: #007bff;
        font-size: 14px;
        font-weight: 500;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<div class="image-gallery-container">
    <? for(var i = 0; i < imageList.length; i++) { ?>
    <div class="image-item">
        <img src="<?= imageList[i].previewUrl ?>" alt="图片<?= i+1 ?>">
        <div class="image-footer">
            <span class="image-number">第<?= i+1 ?>张</span>
            <? if(showEditButton) { ?>
            <button class="edit-image-btn" data-index="<?= i ?>">
                编辑
            </button>
            <? } ?>
        </div>
    </div>
    <? } ?>
</div>
</script>