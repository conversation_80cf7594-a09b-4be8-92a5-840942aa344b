
<script id="dianping-plugin-image-gallery-template-main-index" type="text/html">
<style>
    .image-gallery-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        gap: 15px;
        padding: 10px;
    }

    .image-item {
        position: relative;
        width: 300px;
        margin-bottom: 20px;
        border: 1px solid #eee;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .image-item img {
        width: 100%;
        height: 250px;
        object-fit: contain;
        display: block;
    }

    .image-footer {
        padding: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #f9f9f9;
    }

    .image-number {
        font-size: 14px;
        color: #666;
    }

    .edit-image-btn {
        padding: 5px 10px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
    }

    .edit-image-btn:hover {
        background-color: #0056b3;
    }
</style>

<div class="image-gallery-container">
    <? for(var i = 0; i < imageList.length; i++) { ?>
    <div class="image-item">
        <img src="<?= imageList[i].previewUrl ?>" alt="图片<?= i+1 ?>">
        <div class="image-footer">
            <span class="image-number">第<?= i+1 ?>张</span>
            <? if(showEditButton) { ?>
            <button class="edit-image-btn" data-index="<?= i ?>">
                编辑
            </button>
            <? } ?>
        </div>
    </div>
    <? } ?>
</div>
</script>