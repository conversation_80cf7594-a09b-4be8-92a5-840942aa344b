/*
 * index v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/8/22
 */

"use strict";

define([
  "simple!core/template",
  "simple!core/table",
  "simple!core/utils",
  "simple!core/widgets",
  "simple!core/store",
  "simple!core/form",
  "simple!app/layout/main",
  "dianping!app/user-detail/index",
  'simple!core/plugin',
  'simple!core/ajax',
], function (Template, Table, Utils, Widgets, Store, Form, Layout, UserDetail, Plugin, Ajax) {
  //计算近一周的日期
  var today = new Date().getTime();
  var lastWeekToday = today - 6*24*60*60*1000;
  var startDate = Utils.format.date(lastWeekToday, 'yyyy-MM-dd');
  var endDate = Utils.format.date(today, 'yyyy-MM-dd');

  var isRepeat = false;
  // 点评placeToken查询
  var searchTokenStore =[
    {
    key: 'f1c24bac914c468bbdf47b7d9802f8e7',
    value: "驾校点评",
    },
    {
    key: '07c6d1dc5912405580584c2d02952036',
    value: "教练点评",
    }
  ]
  // 替换文字换行
  const regBrText = function (text) {
    if(text) {
      return text.replace(/\n/g,"<br>")
    } else {
      return text
    }
  }

  // 毫秒格式转化
  function formatDuring(mss) {
    var days = parseInt(mss / (1000 * 60 * 60 * 24));
    var hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    var minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60));
    var seconds = parseInt((mss % (1000 * 60)) / 1000);
    // return days + " 天 " + hours + " 小时 " + minutes + " 分钟 " + seconds + " 秒 ";
    return minutes + " 分钟 " + seconds + " 秒 ";
    }

  var reviewStatus = [
    {
      key: 0,
      value: "待审核",
    },
    {
      key: 1,
      value: "审核通过",
    },
    {
      key: 2,
      value: "审核失败",
    },
    {
      key: 3,
      value: "忽略",
    },
  ];
  //点评状态
  var dianpingStatus = [
    {
      key: 0,
      value: "正常",
    },
    {
      key: 5,
      value: "正常-机审",
    },
    {
      key: 6,
      value: "正常-人审",
    },
    {
      key: 1,
      value: "待审核",
    },
    {
      key: 2,
      value: "已删除",
    },
    {
      key: 3,
      value: "已忽略",
    },
    {
      key: '4',
      value: '审核驳回'
    },
    {
      key: 10,
      value: "延迟展示",
    },
  ];

  var dianpingStatusMap = {
    0: "正常",
    1: "待审核",
    2: "已删除",
    3: "已忽略",
  };
  const confidenceMap = [
    {
      key: "",
      value: "请选择可信度",
    },
    {
      key: 0,
      value: "未知",
    },
    {
      key: 1,
      value: "低",
    },
    {
      key: 2,
      value: "高",
    },
  ];
  const reasonMap = [
    {
      key: "",
      value: "请选择进审原因"
    },
    {
      key: "先审后发",
      value: "先审后发"
    },
    {
      key: "策略",
      value: "策略"
    },
    {
      key: "相似度策略",
      value: "相似度策略"
    }
  ]

  var scrollTop = 0;
  var scrollTop2 = 0;
  // 驳回类型
  var trademarkMap = [
    {
      key: "",
      value: "请选择驳回类型"
    },
    {
      key: "1",
      value: "涉嫌辱骂"
    },
    {
      key: "2",
      value: "疑似广告"
    },
    {
      key: "3",
      value: "涉及个人隐私"
    },
    {
      key: "4",
      value: "点评内容无意义"
    },
    {
        key: "5",
        value: "涉嫌涉政"
    },
  ]

  var list = function (panel) {
    Table(
      {
        description: "点评记录列表",
        title: "点评记录列表",
        search: [
          {
            xtype: "date",
            dataIndex: "dateFrom",
            placeholder: "开始时间",
            value: startDate
          },
          {
            xtype: "date",
            dataIndex: "dateTo",
            placeholder: "结束时间",
            value: endDate
          },
          {
            xtype: "text",
            dataIndex: "id",
            placeholder: "请输入id",
          },
          {
            xtype: "select",
            dataIndex: "searchMode",
            placeholder: "请选择搜索方式",
            store: [
              {
                key: 0,
                value: "模糊搜索",
              },
              {
                key: 1,
                value: "完全匹配",
              },
            ],
          },
          {
            xtype: "select",
            dataIndex: "placeToken",
            index: {
              key: "token",
              value: "name",
            },
            store: "dianping!dianping-place/data/listAll",
            insert: [
              {
                name: "请选择点评位",
                token: "",
              },
            ],
            autoSubmit: false,
          },
          {
            xtype: Plugin('simple!select-district', {
                name: 'cityCode',
                insert: {
                    province: [{
                        code: '',
                        name: '请选择省份'
                    }],
                    city: [{
                        code: '',
                        name: '请选择市'
                    }]
                }
            }),
            dataIndex: 'cityCode'
          },
          {
            xtype: "text",
            dataIndex: "topic",
            placeholder: "请输入topic",
          },
          {
            xtype: "select",
            dataIndex: "status",
            placeholder: "正常",
            store: dianpingStatus,
            autoSubmit: true,
          },
          {
            xtype: "text",
            dataIndex: "authorId",
            placeholder: "请输入authorId",
          },
          // {
          //   xtype: "select",
          //   dataIndex: "credibility",
          //   store: confidenceMap,
          //   placeholder: "请选择可信度",
          // },
          {
            xtype: "text",
            dataIndex: "content",
            placeholder: "请输入content",
          },
          {
            xtype: "select",
            dataIndex: "hasImage",
            placeholder: "请选择是否有图",
            store: [
              {
                key: "",
                value: '请选择是否有图',
              },
              {
                key: 0,
                value: "无图片",
              },
              {
                key: 1,
                value: "有图片",
              },
            ],
          },
        ],
        buttons: {
          top: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
            {
              name: "批量删除",
              class: "warning",
              // click: deleteAll
              click: function (table, dom, config, arr) {
                if (arr.length === 0) {
                  Widgets.dialog.alert("请选择要删除的点评！");
                  return;
                }
                Widgets.dialog.confirm(
                  "确定批量删除这些点评吗？",
                  function (e, confirm) {
                    if (confirm) {
                      Store([
                        "dianping!dianping/data/batchDelete?ids=" +
                          arr.join(","),
                      ])
                        .save()
                        .done(function () {
                          table.render();
                        })
                        .fail(function (data) {
                          Widgets.dialog.alert(data.message);
                        });
                    }
                  }
                );
              },
            },

            {
              name: "删除并禁言",
              class: "danger",
              // click: delAndForbid
              click: function (table, dom, config, idsArr) {
                var paramStr = idsArr.join(",");

                if (idsArr.length == 0) {
                  Widgets.dialog.alert("亲, 你还没勾选评论！");
                } else {
                  Table({
                    title: "设置禁言时长",
                    width: 500,
                    store: "dianping!dianping/data/delAndForbid",
                    success: function (obj, dialog) {
                      Widgets.dialog.alert("成功删除与禁言！");
                      dialog.close();
                      table.render();
                    },
                    columns: [
                      {
                        header: "木仓Id",
                        dataIndex: "ids",
                        xtype: "hidden",
                        value: paramStr,
                      },

                      {
                        header: "是否永久",
                        dataIndex: "forever",
                        xtype: "select",
                        store: [
                          {
                            key: false,
                            value: "暂时禁言",
                          },
                          {
                            key: true,
                            value: "永久禁言",
                          },
                        ],
                      },
                      {
                        header: "禁言天数",
                        dataIndex: "forbiddenDay",
                        xtype: "select",
                        value: "",
                        store: [
                          {
                            key: 1,
                            value: "1天",
                          },
                          {
                            key: 3,
                            value: "3天",
                          },
                          {
                            key: 7,
                            value: "7天",
                          },
                        ],
                      },
                    ],
                    form: {
                      submitHandler: function (form) {
                        var flag = true;
                        if (
                          $(form).find("#forever").val() == true &&
                          $(form).find("#forbiddenDay").val()
                        ) {
                          flag = false;
                          Widgets.dialog.alert("永久禁言时不能填写禁言天数!");
                        }
                        if (
                          $(form).find("#forever").val() == false &&
                          !$(form).find("#forbiddenDay").val()
                        ) {
                          flag = false;
                          Widgets.dialog.alert("暂时禁言时必须填写禁言天数!");
                        }
                        return flag;
                      },
                    },
                  }).add();
                }
              },
            },
            {
              name: "小号发布",
              class: "primary",
              click: createAsRobot,
            },
            {
              name: "批量删除回复",
              class: "success",
              // click: deleteAll
              click: function (table, dom, config, arr) {
                if (arr.length === 0) {
                  Widgets.dialog.alert("请选择要删除的点评回复！");
                  return;
                }
                Widgets.dialog.confirm(
                  "确定批量删除这些点评回复吗？",
                  function (e, confirm) {
                    if (confirm) {
                      Store([
                        "dianping!dianping/data/delReply?dianpingIds=" +
                          arr.join(","),
                      ])
                        .save()
                        .done(function () {
                          table.render();
                        })
                        .fail(function (data) {
                          Widgets.dialog.alert(data.message);
                        });
                    }
                  }
                );
              },
            },
          ],
          bottom: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
          ],
        },
        selector: {
          dataIndex: "id",
        },
        columns: [
          // {
          //     header: function () {
          //         return '<input class="allCheck" type="checkbox">';
          //     },
          //     render: function () {
          //         return '<input class="oCheck" type="checkbox">';
          //     }
          // },
          {
            header: "#",
            dataIndex: "id",
            width: 20,
          },
          {
            header: "主题信息",
            dataIndex: "placeName",
            render: function (data, table, row) {
              return row.placeName + "<br/>" + row.topic;
            },
          },
          {
            header: "用户",
            dataIndex: "author",
            render: function (data, table, row) {
              if(!data) {
                return ''
              }
              return (
                '<div style="cursor: pointer;" class="userinfo" data-userId="' +
                data.mucangId +
                '">' +
                '<img src="' +
                data.avatar +
                '" style="max-width:50px; max-height:50px;border-radius: 25px;">' +
                "<br/>" +
                "<span style=color:" + (row.dianpingRed ? 'red' : '#33333')+ '>' +
                data.nickname +
                "</span>" +
                "</div>"
              );
            },
          },
          {
            header: "最新可信度",
            width: 100,
            dataIndex: "credibility",
            render: (data, arr, lineData) =>
              data === 0
                ? (lineData.registerSameDay ? `未知：${lineData.registerSameDay}` : '未知')
                : data === 1
                ? "低"
                : data === 2
                ? "高"
                : "数据暂未同步",
          },
          {
            header: "可信度更新时间",
            width: 100,
            dataIndex: "credibilityUpdateTime",
            render: (data, arr, lineData) =>{
              if(data) {
                return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
              }
            }
          },
          {
            header: "最初可信度",
            width: 100,
            dataIndex: "originalCredibility",
            render: (data, arr, lineData) =>{
              if(!lineData.credibilityUpdateTime){
                return ''
              } else {
                switch(data){
                  case 1: {
                    return '低';
                  }
                  case 2: {
                    return '高';
                  }
                  default: {
                    return '未知';
                  }
                }
              }
            }
          },
          {
            header: "进审原因",
            width: 80,
            dataIndex: "reason",
          },
          {
            header: "学员评分",
            dataIndex: "score",
            width: 90,
          },
          {
            header: "内容",
            dataIndex: "originalContent",
            width: 400,
            render: function (data, arr, lineData) {
              const title = lineData.originalContent ? lineData.originalContent:  lineData.content;
              var domstr =
                '<div style="padding-bottom: 10px;">' + regBrText(title) + "</div>";
              var domstr1 = "";
              var domstr2 = "";
              var NewreplyTime = "";
              var appendreplyTime = "";
              var extraData = lineData.extraData;
              if (extraData) {
                if (typeof extraData == "string") {
                  extraData = JSON.parse(extraData);
                }
                if (extraData.reply) {
                  domstr1 =
                    '<div style="padding-top: 10px;border-top: 1px solid #cccccc">回复内容：</div><p>' +
                    regBrText(extraData.reply)+
                    '</p><div style="padding-top: 10px;border-top: 1px solid #cccccc">回复时间：</div><p>' +
                    Utils.format.date(
                      extraData.replyTime,
                      "yyyy-MM-dd HH:mm:ss"
                    ) +
                    "</p>";
                  NewreplyTime = Date.parse(
                    new Date(
                      Utils.format.date(
                        extraData.replyTime,
                        "yyyy-MM-dd HH:mm:ss"
                      )
                    )
                  );
                  // console.log(NewreplyTime,'NewreplyTime');
                }
                if (extraData.appendDianping) {
                  domstr2 =
                    '<div style="padding-top: 10px;border-top: 1px solid #cccccc">追评内容：</div><p>' +
                    extraData.appendDianping +
                    '</p><div style="padding-top: 10px;border-top: 1px solid #cccccc">追评时间：</div><p>' +
                    Utils.format.date(
                      extraData.appendTime,
                      "yyyy-MM-dd HH:mm:ss"
                    ) +
                    "</p>";
                  appendreplyTime = Date.parse(
                    new Date(
                      Utils.format.date(
                        extraData.appendTime,
                        "yyyy-MM-dd HH:mm:ss"
                      )
                    )
                  );
                  // console.log(appendreplyTime,'appendTime');
                }
              }
              if (NewreplyTime - appendreplyTime > 0) {
                return (
                  '<div style="max-width: 480px;">' +
                  domstr +
                  domstr2 +
                  domstr1 +
                  "</div>"
                );
              } else {
                return (
                  '<div style="max-width: 480px;">' +
                  domstr +
                  domstr1 +
                  domstr2 +
                  "</div>"
                );
              }
            },
          },
          {
            header: "图片",
            dataIndex: "imageCount",
            width: 100,
            render: function (data, arrData, lineData, index) {
              return  lineData.imageCount > 1 ? "<a>共" + lineData.imageCount + "张图片</a>" :
                      lineData.imageCount > 0 ? '<a><img src="' + lineData.imageList[0].url +'!max800" style="max-width:200px; max-height:200px;"></a>':
                      "";
            },
            click: function (table, row, lineData) {
              Plugin('dianping!image-gallery', {
                imageList: lineData.imageList,
                dianpingId: lineData.id,
                showEditButton: true,
                onSave: function () {
                  table.render();
                }
              }).render();
            },
          },
          {
            header: "楼层",
            dataIndex: "floor",
          },
          {
            header: "引用回复",
            dataIndex: "replyId",
          },
          {
            header: "是否精华",
            render: function (data) {
              if (data) {
                return "是";
              } else {
                return "否";
              }
            },
            dataIndex: "jinghua",
          },
          {
            header: "点赞数量",
            dataIndex: "zanCount",
          },
          {
            header: "地点",
            dataIndex: "location",
          },
          {
            header: "地址",
            dataIndex: "address",
          },
          {
            header: "点评用时",
            dataIndex: "costTime",
            render: function (data) {
              return data && formatDuring(data)
            }
          },
          {
            header: "app版本",
            dataIndex: "version",
          },
          {
            header: "ip属地",
            dataIndex: "ipLocation",
          },
          {
            header: "驾校省份",
            dataIndex: "provinceName",
            render: function (data, table, row) {
              if(data && row && row.cityName) {
                return data + row.cityName
              }
            }
          },
          {
            header: "用户类别",
            dataIndex: "userType",
            class: "userType",
          },
          {
            header: "状态",
            dataIndex: "status",
            render: function (data, table, row) {
              var str = ''
              var styleStr = "";
              try {
                const { key, value } = dianpingStatus.find(item=>item.key===data);
                if(key === 0 && row.machinePass) {
                  str = '正常-机审'
                } else if(key === 0 && !row.machinePass) {
                  str = '正常-人审'
                } else {
                  str = value
                }
                if (str == "已删除") {
                  styleStr = ' style="color: orangered;"';
                }
              } catch(e) {
                styleStr = ' style="color: orangered;"';
                str = '异常状态'
              }
              return "<span" + styleStr + " >" + str + "</span>";
            },
          },
          {
            header: "创建时间",
            dataIndex: "createTime",
            render: function (data) {
              return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
            },
          },
        ],
        operations: [
          {
            name: "查看",
            xtype: "view",
            width: 400,
            class: "success",
            title: "查看",
            store: "dianping!dianping/data/view",
            columns: [
              {
                header: "#",
                dataIndex: "id",
              },
              {
                header: "点评位：",
                dataIndex: "placeToken",
              },
              {
                header: "topic：",
                dataIndex: "topic",
              },
              {
                header: "作者编号：",
                dataIndex: "authorId",
              },
              {
                header: "最新可信度：",
                width: 100,
                dataIndex: "credibility",
                render: (data, arr, lineData) =>
                  data === 0
                    ? (lineData.registerSameDay ? `未知：${lineData.registerSameDay}` : '未知')
                    : data === 1
                    ? "低"
                    : data === 2
                    ? "高"
                    : "数据暂未同步",
              },
              {
                header: "可信度更新时间：",
                width: 100,
                dataIndex: "credibilityUpdateTime",
                render: (data, arr, lineData) =>{
                  if(data) {
                    return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                  }
                }
              },
              {
                header: "最初可信度：",
                width: 100,
                dataIndex: "originalCredibility",
                render: (data, arr, lineData) =>{
                  if(!lineData.credibilityUpdateTime){
                    return ''
                  } else {
                    switch(data){
                      case 1: {
                        return '低';
                      }
                      case 2: {
                        return '高';
                      }
                      default: {
                        return '未知';
                      }
                    }
                  }
                }
              },
              {
                header: "进审原因：",
                dataIndex: "reason",
              },
              {
                header: "楼层：",
                dataIndex: "floor",
              },
              {
                header: "内容：",
                dataIndex: "originalContent",
                render: function (data, arrData, lineData, index) {
                  return lineData.originalContent ? lineData.originalContent : lineData.content
                },
              },
              {
                header: "图片数量：",
                dataIndex: "imageCount",
              },
              {
                header: "驳回类型：",
                dataIndex: "failType",
                render: function (data, arrData, lineData, index) {
                  var failData = trademarkMap.find(item=>item.key===String(data));
                  if (failData && failData.value){
                    return failData.value
                  }
                }
              },
              {
                header: "具体原因：",
                dataIndex: "failReason",
              },
              {
                header: "地点：",
                dataIndex: "location",
              },
              {
                header: "地址：",
                dataIndex: "address",
              },
              {
                header: "引用回复：",
                dataIndex: "replyId",
              },
              {
                header: "是否删除：",
                render: function (data) {
                  if (data) {
                    return "是";
                  } else {
                    return "否";
                  }
                },
                dataIndex: "deleted",
              },
              {
                header: "jinghua：",
                render: function (data) {
                  if (data) {
                    return "是";
                  } else {
                    return "否";
                  }
                },
                dataIndex: "jinghua",
              },
              {
                header: "点赞数量：",
                dataIndex: "zanCount",
              },
              {
                header: "创建时间：",
                dataIndex: "createTime",
              },
              {
                header: "appuser：",
                dataIndex: "appuser",
              },
              {
                header: "imei：",
                dataIndex: "imei",
              },
              {
                header: "appName：",
                dataIndex: "appName",
              },
              {
                header: "product：",
                dataIndex: "product",
              },
              {
                header: "version：",
                dataIndex: "version",
              },
              {
                header: "platform：",
                dataIndex: "platform",
              },
              {
                header: "system：",
                dataIndex: "system",
              },
              {
                header: "pkgName：",
                dataIndex: "pkgName",
              },
              {
                header: "device：",
                dataIndex: "device",
              },
              {
                header: "ip：",
                dataIndex: "ip",
              },
              {
                header: "longitude：",
                dataIndex: "longitude",
              },
              {
                header: "latitude：",
                dataIndex: "latitude",
              },
            ],
          },
          {
            name: "审核",
            class: "info",
            render: function (name, lineList, index) {
              var status = lineList[index].status;
              if (status == 1) {
                return "审核通过";
              } else {
                return ""; // status只有在1才行，不然就不显示关于审核的按钮
              }
            },
            click: function (table, row, lineData) {
              scrollTop = $(panel).scrollTop();
              Store([
                "dianping!dianping/data/audit?ids=" + lineData.id + "&status=0",
              ])
                .save()
                .done(function (store, data) {
                  table.render();
                })
                .fail(function (data) {
                  Widgets.dialog.alert(data.message);
                });
            },
          },
          {
            name: "审核失败",
            class: "info",
            render: function (name, lineList, index) {
              var status = lineList[index].status;
              if (status == 1) {
                return "审核失败";
              } else {
                return ""; //status只有在1才行，不然就不显示关于审核的按钮
              }
            },
            click: function (table, row, lineData) {
              scrollTop = $(panel).scrollTop();
              Store([
                "dianping!dianping/data/audit?ids=" + lineData.id + "&status=2",
              ])
                .save()
                .done(function (store, data) {
                  table.render();
                })
                .fail(function (data) {
                  Widgets.dialog.alert(data.message);
                });
            },
          },
          {
            name: "设为精华",
            class: "warning",
            render: function (name, lineList, index) {
              var jinghua = lineList[index].jinghua;
              if (jinghua === false && lineList[index].status === 0) {
                return "设为精华";
              } else {
                return ""; //status只有在1才行，不然就不显示关于审核的按钮
              }
            },
            click: function (table, row, lineData) {
              scrollTop = $(panel).scrollTop();
              Store(["dianping!dianping/data/addJinghua?id=" + lineData.id])
                .save()
                .done(function (store, data) {
                  table.render();
                })
                .fail(function (data) {
                  Widgets.dialog.alert(data.message);
                });
            },
          },
          {
            name: "复审",
            class: "warning",
            click: function (table, lineDom, lineData) {
              Widgets.dialog.html('请选择复审结果', {
                width: 400,
                buttons: [{
                  name: '审核通过',
                  xtype: 'success',
                  click: function () {
                    const that = this;
                    Store(["dianping!dianping/data/audit?ids=" + lineData.dianpingId + "&status=0"])
                    .save()
                    .done(function () {
                      that.close();
                      table.render();
                    })
                    .fail(function (data) {
                      Widgets.dialog.alert(data.message);
                    });
                  }
              }, {
                  name: '审核失败',
                  xtype: 'primary',
                  click: function () {
                    const that = this;
                    Store(["dianping!dianping/data/audit?ids=" + lineData.dianpingId + "&status=4"])
                    .save()
                    .done(function () {
                      that.close();
                      table.render();
                    })
                    .fail(function (data) {
                      Widgets.dialog.alert(data.message);
                    });
                  }
              }]
              })
            }
          },
          {
            name: "取消精华",
            class: "danger",
            render: function (name, lineList, index) {
              var jinghua = lineList[index].jinghua;
              if (jinghua == true) {
                return "取消精华";
              } else {
                return ""; //status只有在1才行，不然就不显示关于审核的按钮
              }
            },
            click: function (table, row, lineData) {
              scrollTop = $(panel).scrollTop();
              Store(["dianping!dianping/data/removeJinghua?id=" + lineData.id])
                .save()
                .done(function (store, data) {
                  table.render();
                })
                .fail(function (data) {
                  Widgets.dialog.alert(data.message);
                });
            },
          },
          {
            name: "删除",
            class: "danger",
            render: function (name, objArr, index) {
              var val = "";
              if (objArr[index].status !== 2) {
                val = "删除";
              }
              return val;
            },
            click: function (table, row, lineData) {
              scrollTop = $(panel).scrollTop();
              Widgets.dialog.confirm(
                "确定删除 #" + lineData.id + " 这条点评吗？",
                function (e, status) {
                  if (status) {
                    Store(["dianping!dianping/data/delete?id=" + lineData.id])
                      .save()
                      .done(function () {
                        table.render();
                      })
                      .fail(function (data) {
                        Widgets.dialog.alert(data.message);
                      });
                  }
                }
              );
            },
          },
          {
            name: "恢复",
            class: "primary",
            render: function (name, lineList, index) {
              // 只有删除和忽略状态才展示恢复按钮
              return lineList[index].status === 2 ||
                lineList[index].status === 3
                ? "恢复"
                : "";
            },
            click: function (table, row, lineData) {
              Widgets.dialog.confirm(
                "确定恢复 #" + lineData.id + " 这条点评吗？",
                function (e, status) {
                  if (status) {
                    Store([
                      "dianping!dianping/data/recover?dianpingId=" +
                        lineData.id,
                    ])
                      .save()
                      .done(function () {
                        table.render();
                      })
                      .fail(function (data) {
                        Widgets.dialog.alert(data.message);
                      });
                  }
                }
              );
            },
          },
          {
            name: "查看用户",
            class: "info",
            click: function (table, row, lineData) {
              var panel = Layout.panel({
                id: "user-" + lineData.id,
                name: lineData.author.nickname,
              });
              UserDetail.edit(panel, {
                id: lineData.id,
                authorId: lineData.authorId,
              });
            },
          },
          {
            name: "删除回复",
            class: "danger",
            render: function (name, objArr, index) {
              var val = "";
              if (
                objArr[index].extraData &&
                JSON.parse(objArr[index].extraData).reply
              ) {
                val = "删除回复";
              }
              return val;
            },
            click: function (table, row, lineData) {
              scrollTop = $(panel).scrollTop();
              Widgets.dialog.confirm(
                "确定删除 #" + lineData.id + " 这条点评的回复吗？",
                function (e, status) {
                  if (status) {
                    Store([
                      "dianping!dianping/data/delReply?dianpingId=" +
                        lineData.id,
                    ])
                      .save()
                      .done(function () {
                        table.render();
                      })
                      .fail(function (data) {
                        Widgets.dialog.alert(data.message);
                      });
                  }
                }
              );
            },
          },
          {
            name: "删除追评",
            class: "danger",
            render: function (name, objArr, index) {
              var val = "";
              if (
                objArr[index].extraData &&
                JSON.parse(objArr[index].extraData).appendDianping
              ) {
                val = "删除追评";
              }
              return val;
            },
            click: function (table, row, lineData) {
              scrollTop = $(panel).scrollTop();
              Widgets.dialog.confirm(
                "确定删除 #" + lineData.id + " 这条点评的追评吗？",
                function (e, status) {
                  console.log(lineData, 921);
                  if (status) {
                    Store([
                      "dianping!dianping/data/delProdu?dianpingId=" +
                        lineData.id,
                    ])
                      .save()
                      .done(function () {
                        table.render();
                      })
                      .fail(function (data) {
                        Widgets.dialog.alert(data.message);
                      });
                  }
                }
              );
            },
          },
          {
            name: '隐藏',
            class: "warning",
            render: function (name, lineList, index) {
              var hiddenButtonDisable = lineList[index].hiddenButtonDisable;
              if (hiddenButtonDisable) {
                return "";
              } else {
                return "隐藏";
              }
            },
            click: function (table, lineDom, lineData) {
              Widgets.dialog.html('确定隐藏点评吗？','<textarea id="hiddenRemarks" placeholder="输入通过原因，必填" cols="50" />', {
                width: 400,
                buttons: [{
                  name: '确认',
                  xtype: 'success',
                  click: function () {
                    const that = this;
                    const remarks = $('#hiddenRemarks').val().trim();
                    if (!remarks) {
                        Widgets.dialog.alert('请输入隐藏原因');
                        return;
                    }

                    Simple.Store2('dianping!dianping/data/applyHiddenStore', {
                      id: lineData.id,
                      remarks: remarks
                    }, 'save').then(function () {
                      that.close();
                      table.render();
                    },function (data) {
                      Widgets.dialog.alert(data.message);
                    });
                  }
                }, {
                  name: '关闭',
                  xtype: 'primary',
                  click: function () {
                    this.close();
                  }
                }]
              })
            }
          },
          {
            name: '屏蔽关键字',
            class: "success",
            click: function (table, lineDom, lineData) {
              Widgets.dialog.html(
                  '选择关键字',
                  (lineData.originalContent || lineData.content) + '<br><br><span>填写要屏蔽的关键字</span><textarea id="hiddenText" placeholder="填写关键字或关键词,不同的关键词请用英文的逗号隔开" cols="50" />',
                  {
                width: 400,
                buttons: [{
                  name: '确认',
                  xtype: 'success',
                  click: function () {
                    const that = this;
                    const remarks = $('#hiddenText').val().trim();
                    if (!remarks) {
                      Widgets.dialog.alert('请输入需要屏蔽的关键字');
                      return;
                    }

                    Simple.Store2('dianping!dianping/data/applyHiddenStore', {
                      id: lineData.id,
                      remarks: remarks
                    }, 'save').then(function () {
                      that.close();
                      table.render();
                    },function (data) {
                      Widgets.dialog.alert(data.message);
                    });
                  }
                }, {
                  name: '关闭',
                  xtype: 'primary',
                  click: function () {
                    this.close();
                  }
                }]
              })
            }
          }
        ],
      },
      ["dianping!dianping/data/list?status=0"],
      panel,
      function (target, config, item, table) {
        $(panel).scrollTop(scrollTop);
        $(".allCheck").on("click", function () {
          $(".oCheck").click();
        });

        $(".userinfo").on("click", function () {
          $(this).parent().parent().find("[title=查看用户]").click();
        });
      }
    ).render();
  };

  var deleteAll = function (Table) {
    //检测哪些checkbox被打勾了 记录ids
    var idsArr = [];
    var myIds = "";
    var paramStr = "";
    $("input[class='oCheck']:checkbox").each(function () {
      if ($(this)[0].checked == true) {
        myIds = $.trim($(this).parent().parent().find("td[title='#']").text()); //string   本行的#(id)
        idsArr.push(parseInt(myIds));
      }
    });

    paramStr = idsArr.join(",");
    Store(["dianping!dianping/data/batchDelete?ids=" + paramStr])
      .save()
      .done(function () {
        Table.render();
      })
      .fail(function (data) {
        Widgets.dialog.alert(data.message);
      });
  };

  var delAndForbid = function (table) {
    //检测哪些checkbox被打勾了 记录ids
    var idsArr = [];
    var myIds = "";
    var paramStr = "";
    $("input[class='oCheck']:checkbox").each(function () {
      if ($(this)[0].checked == true) {
        myIds = $.trim($(this).parent().parent().find("td[title='#']").text()); //string   本行的#(id)
        idsArr.push(parseInt(myIds));
      }
    });

    paramStr = idsArr.join(",");

    if (idsArr.length == 0) {
      Widgets.dialog.alert("亲, 你还没勾选评论！");
    } else {
      Table({
        title: "设置禁言时长",
        width: 500,
        store: "dianping!dianping/data/delAndForbid",
        success: function (obj, dialog) {
          Widgets.dialog.alert("成功删除与禁言！");
          dialog.close();
          table.render();
        },
        columns: [
          {
            header: "木仓Id",
            dataIndex: "ids",
            xtype: "hidden",
            value: paramStr,
          },

          {
            header: "是否永久",
            dataIndex: "forever",
            xtype: "select",
            store: [
              {
                key: false,
                value: "暂时禁言",
              },
              {
                key: true,
                value: "永久禁言",
              },
            ],
          },
          {
            header: "禁言天数",
            dataIndex: "forbiddenDay",
            xtype: "select",
            value: "",
            store: [
              {
                key: 1,
                value: "1天",
              },
              {
                key: 3,
                value: "3天",
              },
              {
                key: 7,
                value: "7天",
              },
            ],
          },
        ],
        form: {
          submitHandler: function (form) {
            var flag = true;
            if (
              $(form).find("#forever").val() == true &&
              $(form).find("#forbiddenDay").val()
            ) {
              flag = false;
              Widgets.dialog.alert("永久禁言时不能填写禁言天数!");
            }
            if (
              $(form).find("#forever").val() == false &&
              !$(form).find("#forbiddenDay").val()
            ) {
              flag = false;
              Widgets.dialog.alert("暂时禁言时必须填写禁言天数!");
            }
            return flag;
          },
        },
      }).add();
    }
  };

  var getSlctedIds = function (Table) {
    //检测哪些checkbox被打勾了 记录ids
    var idsArr = [];
    var myIds = "";
    var paramStr = "";
    $("input[class='oCheck']:checkbox").each(function () {
      if ($(this)[0].checked == true) {
        myIds = $.trim($(this).parent().parent().find("td[title='#']").text()); //string   本行的#(id)
        idsArr.push(parseInt(myIds));
      }
    });
    paramStr = idsArr.join(",");
    return paramStr;
  };
  var getSelectedIds = function (dom) {
    var idsArr = [];
    var myIds = "";
    dom.find("input[class='oCheck']:checkbox").each(function () {
      if ($(this)[0].checked == true) {
        myIds = $.trim($(this).parent().parent().find("td[title='#']").text()); //string   本行的#(id)
        idsArr.push(parseInt(myIds));
      }
    });
    return idsArr.join(",");
  };
  var letFail = function (Tables, paramStr) {
    if (paramStr.length <= 0) {
      Widgets.dialog.alert("请勾选数据");
      return;
    }
    isAudit(Tables, paramStr, "fail");
    // var paramStr = getSlctedIds();
    // Store(['dianping!dianping/data/audit?ids=' + paramStr + '&pass=false']).save().done(function () {
    // Store(['dianping!dianping/data/audit?ids=' + paramStr + '&status=2']).save().done(function () {
    //     Table.render();
    // }).fail(function (data) {
    //     Widgets.dialog.alert(data.message);
    // });
  };
  var getTableList = [];
  // 屏蔽关键字字段记录
  var shieldContentArr = [];
  // 屏蔽关键字弹窗
  var shieldDialog = function(lineData) {
    Widgets.dialog.html('选择关键字',
    function(){
      return `<div>
        <p>${lineData.content}</p>
        <p style="color=#ccc">填写要屏蔽的关键字</p>
        <textarea id="failReason" placeholder="填写关键字或关键词，不同的关键词请用英文的逗号隔开" cols="50" />
      </div>`
    },
    {
      width: 400,
      buttons: [{
        name: '确认',
        xtype: 'success',
        click: function () {
          const that = this;
          const failReason = $('#failReason').val().trim();
          if (failReason) {
            var index = shieldContentArr.findIndex(item=>item.id===lineData.dianpingId);
            // 防止数据重复添加
            if(index > -1) {
              shieldContentArr = shieldContentArr.splice(1, index);
            }
            shieldContentArr.push({
              id: lineData.dianpingId,
              content: failReason || '',
              editContent: lineData.content
            });
          }
          setTimeout(function() {
            that.close();
          },200)
        }
    },{
        name: '关闭',
        xtype: 'primary',
        click: function () {
            this.close();
        }
    }]
    })
  }
  //审核失败数据
  var checkFailReasonArr = [];
  // 审核失败操作按钮按钮
  var checkFailReasonDialog = function(lineData) {
    Widgets.dialog.html('选择关键字',
    function(){
      return `<div>
        <div>
          <label>驳回类型</label>
          <select name="pets" id="pet-select">
            <option value="">请选择驳回类型</option>
            <option value="1">涉嫌辱骂</option>
            <option value="2">疑似广告</option>
            <option value="3">涉及个人隐私</option>
            <option value="4">点评内容无意义</option>
            <option value="5">涉嫌涉政</option>
          </select>
        </div>
        <div>
          <label>具体原因</label>
          <textarea id="failReason" placeholder="输入具体原因" cols="50" />
        </div>
      </div>`
    },
    {
      width: 400,
      buttons: [{
        name: '确认',
        xtype: 'success',
        click: function () {
          const that = this;
          const failReason = $('#failReason').val().trim();
          const petSelectReason = $('#pet-select').val().trim();
          var index = checkFailReasonArr.findIndex(item=>item.id===lineData.dianpingId);
          // 防止数据重复添加
          if(index > -1) {
            checkFailReasonArr = checkFailReasonArr.splice(1, index);
          }
          checkFailReasonArr.push({
            id: lineData.dianpingId,
            failType: petSelectReason,
            failReason: failReason
          })
          setTimeout(function() {
            that.close();
          },200)
        }
    },{
        name: '关闭',
        xtype: 'primary',
        click: function () {
            this.close();
        }
    }]
    })
  }
  let isAudit = function (Tables, paramStr, type) {
    let hasRisk = [];
    let hasSelectArray = [];
    // 记录勾选的数据不是"先审后发类型数据"
    let issueLaterCount = 0
    hasRisk = getTableList.filter((ele) => {
      paramStr.forEach((arr) => {
        if (arr == ele.id) {
          hasSelectArray.push(ele.id);
        }
      });
      let tag1 =
        (ele.reason?.length == 4 && ele.reason == "先审后发") ||
        ele.reason?.length == 0;
        // 审核失败不做过滤
      const approveFail = true;
      let tag2 = type == "pass" ? !tag1 : approveFail;
      return paramStr.indexOf(ele.id + "") !== -1 && tag2;
    });
    var reductArray = [];
    // 服务端没有接口数据没有返回状态码，只能使用中文做判断
    hasRisk.forEach((item) => {
      if(type == "fail" && !((item.reason.length == 4 && item.reason == "先审后发") || item.reason.length == 0)){
        issueLaterCount+=1
      }
    })
    Widgets.dialog
      .html(type == "pass" ? "请确认 - 审核通过" : "请确认 - 审核失败", {
        height: 600,
        width: 900,
        buttons: [
          {
            name: type == "pass" ? "审核通过" : "审核失败",
            xtype: type == "pass" ? "primary" : "warning",
            id: "submit-tips",
            class: "submit-tips",
            click: function (obj) {
              var getSelectRadio = document.getElementsByClassName(
                "monidianjuimonidianjui"
              );
              $(getSelectRadio).click();
              let noChecked = hasRisk.filter((ele) => {
                return reductArray.indexOf(ele.id + "") == -1;
              });
              let noCheckedId = [];
              noChecked.forEach((ele) => {
                noCheckedId.push(ele.id);
              });
              let newArray = hasSelectArray.filter((ele) => {
                return noCheckedId.indexOf(ele) == -1;
              });
              let ids = newArray.join(",");
              // 屏蔽的内容
              getTableList.forEach(item=>{
                const index = shieldContentArr.findIndex(f=>f.id===item.dianpingId);
                if (index>-1) {
                  let reasonArr = shieldContentArr[index].content.split(",");
                  // 多个字符替换
                  reasonArr.forEach(obj=>{
                    shieldContentArr[index].editContent = shieldContentArr[index].editContent.replace(new RegExp(obj, 'g'), '*');
                  })
                }
              })
              this.close();
              if (type == "pass") {
                Simple.Store2('dianping!dianping/data/audit', {
                  ids: ids,
                  status: 0,
                  editList: JSON.stringify(shieldContentArr)
              }, 'save').then(function () {
                  Tables.render();
                   // 清空数据
                  shieldContentArr = []
              },function (data) {
                  Widgets.dialog.alert(data.message);
                  shieldContentArr = []
              });
              } else {
                Simple.Store2('dianping!dianping/data/audit', {
                  ids: ids,
                  status: 4,
                  editList: JSON.stringify(checkFailReasonArr)
              }, 'save').then(function () {
                  Tables.render();
                   // 清空数据
                  checkFailReasonArr = []
              },function (data) {
                  Widgets.dialog.alert(data.message);
                  checkFailReasonArr = []
              });
              }
            },
          },
          {
            name: "取消",
            xtype: "warning",
            click: function () {
              this.close();
              // 清空数据
              checkFailReasonArr = []
            },
          },
        ],
      })
      .done(function (dialog) {
        setTimeout(() => {
          let parent = dialog.body;
          var string1 = type == "pass" ? "提示风险" : "建议可通过";
          var string2 = type == "pass" ? "审核通过" : "审核失败";
          var string3 = "请将需要【" + string2 + "】的内容勾选";
          // 审核失败记录
          const tipCount =  type == "fail" ? hasRisk.length - issueLaterCount : hasRisk.length
          dialog.body.prepend(
            "<div style='position:absolute;top:0px;width:96%;background:#fff;height:60px;margin-bottom:30px'><div  style='color:rgb(163,0,142);padding-right:30px;font-size:18px;margin-top:10px'>已勾选" +
              hasSelectArray.length +
              "</span> 条, 请注意甄别有<span id='tips-title2'>" +
              tipCount
              +
              "</span>条机器" +
              string1 +
              ", 请再次核对↓↓↓↓↓ </div><div  style='color:rgb(163,0,142);padding-right:30px;font-size:18px;margin-top:0px'>" +
              string3 +
              "</div></div>"
          );
          let lastParent = parent[0].parentNode.parentNode.parentNode;
          let childrens = $(lastParent).children();
          let footer = childrens[2];
          let footerdiv = $(footer).children();
          $(footerdiv[0]).prepend(
            "<span  style='color:rgb(163,0,142);padding-right:30px;font-size:15px;margin-top:30px'>上方列表里勾选的内容会被“" +
              string2 +
              "”请核对后再点击→</span>"
          );
        }, 0);
        Table(
          {
            width: 800,
            style: {
              marginTop: 30,
            },
            selector: {
              dataIndex: "id",
            },
            buttons: {
              top: [
                {
                  name: "",
                  class: "primary monidianjuimonidianjui",
                  click: function (table, dom, config, arr) {
                    reductArray = arr;
                  },
                },
              ],
            },
            columns: [
              // {    width: 28,
              //     header: function () {
              //         return '<input style="width:28px;height:26px" class="allCheck" checked=true type="checkbox">';
              //     },
              //     render: function () {
              //         return '<input style="width:28px;height:26px" class="oCheck" checked=true type="checkbox">';
              //     }
              // },
              {
                header: "#",
                dataIndex: "id",
                width: 80,
              },
              {
                header: "进审原因",
                dataIndex: "reason",
                width: 80,
                render: function (data) {
                  return (
                    "<div style='white-space: pre-line;'>" + data + "</div>"
                  );
                },
              },
              {
                header: "内容",
                dataIndex: "content",
                width: 500,
                render: function (data) {
                  return '<div style="max-width: 450px;">' + data + "</div>";
                },
              },
              {
                header: "图片",
                dataIndex: "imageCount",
                render: function (data, arrData, lineData, index) {
                  return lineData.imageCount > 0
                    ? "<a>" + lineData.imageCount + "图</a>"
                    : "";
                },
                click: function (table, row, lineData) {
                  var str = "";
                  var count = 0;
                  for (var i = 0; i < lineData.imageList.length; i++) {
                    count++;
                    str +=
                      '<img style="" src="' +
                      lineData.imageList[i].url +
                      '!max800" width="300" height="300">' +
                      "第" +
                      count +
                      "张";
                  }
                  Widgets.dialog.html(+lineData.imageCount + "图", str, {
                    width: 800,
                  });
                },
              },
            ],
            operations: [
              {
                name: type==='pass' ? "屏蔽关键字" : '',
                class: "info",
                click: function (table, row, lineData) {
                  shieldDialog(lineData)
                }
              },
              {
                name: type==='fail' ? "操作" : '',
                class: "info",
                click: function (table, row, lineData) {
                  checkFailReasonDialog(lineData)
                }
              },
            ]
          },
          { data: hasRisk },
          $(dialog.body),
          function (target, table, item) {
            let tag = target.item("description");
            if (tag[0]) {
              tag[0].style.color = "rgb(163,0,142)";
              tag[0].style.fontSize = "18px";
            }
            let checkbox = item("table-selector");
            let checkboxHeader = item("table-header-selector");
            $(checkbox).css({ width: "28px", height: "26px" });
            $(checkboxHeader).css({ width: "28px", height: "26px" });
            $(target[0]).css("marginTop", "60px");
            if (hasRisk.length > 0) {
              $(checkbox).attr("checked", true);
              $(checkboxHeader).attr("checked", true);
            }
          }
        ).render();
      });
  };
  //审核
  var letPass = function (Tables, paramStr) {
    if (paramStr.length <= 0) {
      Widgets.dialog.alert("请勾选数据");
      return;
    }
    isAudit(Tables, paramStr, "pass");
  };

  var ignore = function (Table, paramStr) {
    // var paramStr = getSlctedIds();
    Store(["dianping!dianping/data/audit?ids=" + paramStr + "&status=3"])
      .save()
      .done(function () {
        Table.render();
      })
      .fail(function (data) {
        Widgets.dialog.alert(data.message);
      });
  };

  // 小号发布
  var createAsRobot = function (table) {
    Table({
      title: "小号发布",
      width: 500,
      store: "dianping!dianping/data/createAsRobot",
      success: function (obj, dialog) {
        dialog.close();
        table.render();
      },
      columns: [
        {
          header: "点评位：",
          xtype: "select",
          dataIndex: "placeToken",
          index: {
            key: "token",
            value: "name",
          },
          store: "dianping!dianping-place/data/listAll",
          insert: [
            {
              name: "请选择点评位",
              token: "",
            },
          ],
        },
        {
          header: "点评主题：",
          dataIndex: "topic",
          xtype: "text",
          maxlength: 32,
          check: "required",
          placeholder: "点评主题",
        },
        {
          header: "内容",
          dataIndex: "contentText",
          xtype: "textarea",
          placeholder: "一行一条，最多不能超过20条",
        },
      ],
    }).add();
  };
  // 待审核列表
  var listPending = function (panel) {
    Table(
      {
        description: "待审核点评",
        title: "待审核点评",
        search: [
          {
            xtype: "text",
            dataIndex: "dianpingId",
            placeholder: "请输入id",
          },
          {
            xtype: Plugin('simple!select-district', {
                name: 'cityCode',
                insert: {
                    province: [{
                        code: '',
                        name: '请选择省份'
                    }],
                    city: [{
                        code: '',
                        name: '请选择市'
                    }]
                }
            }),
            dataIndex: 'cityCode'
          },
          {
            xtype: "select",
            dataIndex: "placeToken",
            autoSubmit: true,
            value:'驾校点评',
            store: searchTokenStore,
          },
          {
            xtype: "number",
            dataIndex: "topic",
            placeholder: "请输入驾校id或教练id",
          },
          {
            xtype: "text",
            dataIndex: "authorId",
            placeholder: "请输入用户木仓id",
          },
          // {
          //   xtype: "select",
          //   dataIndex: "credibility",
          //   store: confidenceMap,
          //   placeholder: "请选择可信度",
          // },
          // {
          //   xtype: "select",
          //   dataIndex: "status",
          //   placeholder: "正常",
          //   store: dianpingStatus,
          //   autoSubmit: true,
          // },
          {
            xtype: "select",
            maxlength: 30,
            dataIndex: "reason",
            autoSubmit: true,
            store: reasonMap,
            value: '策略',
          },
          {
            xtype: "select",
            dataIndex: "scoreLevel",
            autoSubmit: true,
            store: [
              {
                key: '',
                value: "请选择评分",
              },
              {
                key: '1',
                value: "差评",
              },
              {
                key: '2',
                value: "中评",
              },
              {
                key: '3',
                value: "好评",
              }
            ],
          },
          {
            xtype: "select",
            dataIndex: "abnormal",
            autoSubmit: true,
            store: [
              {
                key: '',
                value: "请选择主体异常新增",
              },
              {
                key: true,
                value: "是",
              },
              {
                key: false,
                value: "否",
              }
            ],
          },
          {
            xtype: "text",
            dataIndex: "startVersion",
            placeholder: "APP起始版本",
          },
          {
            xtype: "text",
            dataIndex: "endVersion",
            placeholder: "APP结束版本",
          },
        ],
        buttons: {
          top: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
            {
              name: "审核通过",
              class: "primary",
              // click: letPass
              click: function (table, dom, config, arr) {
                letPass(table, arr);
              },
            },
            {
              name: "审核失败",
              class: "warning",
              // click: letFail
              click: function (table, dom, config, arr) {
                letFail(table, arr);
              },
            },
            // {
            //   name: "忽略审核",
            //   class: "danger",
            //   // click: ignore
            //   click: function (table, dom, config, arr) {
            //     ignore(table, arr.join(","));
            //   },
            // },
          ],
          bottom: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
            {
              name: "审核通过",
              class: "primary",
              // click: letPass
              click: function (table, dom, config, arr) {
                letPass(table, arr);
              },
            },
            {
              name: "审核失败",
              class: "warning",
              // click: letFail
              click: function (table, dom, config, arr) {
                letFail(table, arr);
              },
            },
          ],
        },
        operations: [
          //行点击事件
          // {
          //     name: '',
          //     lineSelector: true,
          //     click: function (table, row, lineData) {
          //         let domE = row[0]
          //         let children = $(domE).children()
          //         let checkBox = children[0]
          //         let input = $(checkBox).find("input")[0]
          //         let $inputDom = $(input)
          //         let checked = $inputDom.prop("checked")
          //         if (checked) {
          //             $inputDom.removeAttr("checked")
          //         } else {
          //             $inputDom.prop("checked", true)

          //         }

          //     }
          // },
          {
            name: "查看",
            xtype: "view",
            width: 400,
            class: "success",
            title: "查看",
            store: "dianping!dianping/data/view",
            columns: [
              {
                header: "#",
                dataIndex: "id",
              },
              {
                header: "点评位：",
                dataIndex: "placeToken",
              },
              {
                header: "topic：",
                dataIndex: "topic",
              },
              {
                header: "作者编号：",
                dataIndex: "authorId",
              },
              {
                header: "最新可信度：",
                dataIndex: "credibility",
                width: 100,
                render: (data, arr, lineData) =>
                  data === 0
                    ? (lineData.registerSameDay ? `未知：${lineData.registerSameDay}` : '未知')
                    : data === 1
                    ? "低"
                    : data === 2
                    ? "高"
                    : "数据暂未同步",
              },
              {
                header: "可信度更新时间：",
                dataIndex: "credibilityUpdateTime",
                render: (data, arr, lineData) =>{
                  if(data) {
                    return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                  }
                }
              },
              {
                header: "最初可信度：",
                width: 100,
                dataIndex: "originalCredibility",
                render: (data, arr, lineData) =>{
                  if(!lineData.credibilityUpdateTime){
                    return ''
                  } else {
                    switch(data){
                      case 1: {
                        return '低';
                      }
                      case 2: {
                        return '高';
                      }
                      default: {
                        return '未知';
                      }
                    }
                  }
                }
              },
              {
                header: "楼层：",
                dataIndex: "floor",
              },
              {
                header: "内容：",
                dataIndex: "content",
              },
              {
                header: "图片数量：",
                dataIndex: "imageCount",
              },
              {
                header: "地点：",
                dataIndex: "location",
              },
              {
                header: "地址：",
                dataIndex: "address",
              },
              {
                header: "点评用时",
                dataIndex: "costTime",
                render: function (data) {
                  return data && formatDuring(data)
                }
              },
              {
                header: "APP版本",
                dataIndex: "version",
              },
              {
                header: "引用回复：",
                dataIndex: "replyId",
              },
              {
                header: "是否删除：",
                render: function (data) {
                  if (data) {
                    return "是";
                  } else {
                    return "否";
                  }
                },
                dataIndex: "deleted",
              },
              {
                header: "jinghua：",
                render: function (data) {
                  if (data) {
                    return "是";
                  } else {
                    return "否";
                  }
                },
                dataIndex: "jinghua",
              },
              {
                header: "点赞数量：",
                dataIndex: "zanCount",
              },
              {
                header: "创建时间：",
                dataIndex: "createTime",
              },
              {
                header: "appuser：",
                dataIndex: "appuser",
              },
              {
                header: "imei：",
                dataIndex: "imei",
              },
              {
                header: "appName：",
                dataIndex: "appName",
              },
              {
                header: "product：",
                dataIndex: "product",
              },
              {
                header: "version：",
                dataIndex: "version",
              },
              {
                header: "platform：",
                dataIndex: "platform",
              },
              {
                header: "system：",
                dataIndex: "system",
              },
              {
                header: "pkgName：",
                dataIndex: "pkgName",
              },
              {
                header: "device：",
                dataIndex: "device",
              },
              {
                header: "ip：",
                dataIndex: "ip",
              },
              {
                header: "longitude：",
                dataIndex: "longitude",
              },
              {
                header: "latitude：",
                dataIndex: "latitude",
              },
            ],
          },
          {
            name: "审核",
            class: "info",
            render: function (name, lineList, index) {
              var status = lineList[index].status;
              if (status == 1) {
                return "审核通过";
              } else {
                return ""; //status只有在1才行，不然就不显示关于审核的按钮
              }
            },
            click: function (table, row, lineData) {
              scrollTop2 = $(panel).scrollTop();
              Store([
                "dianping!dianping/data/audit?ids=" + lineData.id + "&status=0",
              ])
                .save()
                .done(function (store, data) {
                  table.render();
                })
                .fail(function (data) {
                  Widgets.dialog.alert(data.message);
                });
            },
          },
          {
            name: "审核失败",
            class: "info",
            render: function (name, lineList, index) {
              var status = lineList[index].status;
              if (status == 1) {
                return "审核失败";
              } else {
                return ""; //status只有在1才行，不然就不显示关于审核的按钮
              }
            },
            click: function (table, row, lineData) {
              scrollTop2 = $(panel).scrollTop();
              Store([
                "dianping!dianping/data/audit?ids=" + lineData.id + "&status=2",
              ])
                .save()
                .done(function (store, data) {
                  table.render();
                })
                .fail(function (data) {
                  Widgets.dialog.alert(data.message);
                });
            },
          },
          {
            name: "删除",
            class: "danger",
            render: function (name, objArr, index) {
              var val = "";
              if (objArr[index].status !== 2) {
                val = "删除";
              }
              return val;
            },
            click: function (table, row, lineData) {
              scrollTop2 = $(panel).scrollTop();
              Widgets.dialog.confirm(
                "确定删除 #" + lineData.id + " 这条点评吗？",
                function (e, status) {
                  if (status) {
                    Store(["dianping!dianping/data/delete?id=" + lineData.id])
                      .save()
                      .done(function () {
                        //panel.trigger('close');
                        table.render();
                      })
                      .fail(function (data) {
                        Widgets.dialog.alert(data.message);
                      });
                  }
                }
              );
            },
          },
          {
            name: "查看用户",
            class: "info",
            click: function (table, row, lineData) {
              var panel = Layout.panel({
                id: "user-" + lineData.id,
                name: lineData.author.nickname,
              });
              UserDetail.edit(panel, {
                id: lineData.id,
                authorId: lineData.authorId,
              });
            },
          },
          {
            name: "查看当前待审",
            class: "info",
            click: function (table, row, lineData) {
              pendingTriaList(table, row, lineData)
            }
          },
          {
            name: "众包核验作者",
            class: "info",
            click: function (table, row, lineData) {
              if(!isRepeat && lineData.manualCkStatus === 0 ) {
                isRepeat = true
                Store([
                  `dianping!dianping/data/applyToCheckWithMsgStore?reviewIds=${lineData.dbId}`,
                ])
                  .save()
                  .done(function (store, data) {
                    table.render();
                    setTimeout(function(){
                      isRepeat = false
                    },50)
                  })
                  .fail(function (data) {
                    Widgets.dialog.alert(data.message);
                    setTimeout(function() {
                      isRepeat = false
                    },50)
                  });
              } else {
                var text;
                switch(lineData.manualCkStatus) {
                  case 1: {
                    text = '核验申请中';
                    break
                  }
                  case 2: {
                    text= '核验存在';
                    break
                  }
                  case 3: {
                    text= '核验不存在';
                    break
                  }
                  default: {
                    text = '数据已被处理';
                    break
                  }
                }
                Widgets.dialog.alert(`该条数据${text}`);
              }
            },
          },
        ],
        selector: {
          dataIndex: "id",
        },
        columns: [
          // {
          //     header: function () {
          //         return '<input class="allCheck" type="checkbox">';
          //     },
          //     render: function () {
          //         return '<input class="oCheck" type="checkbox">';
          //     }
          // },
          {
            header: "点评id",
            dataIndex: "id",
            width: 20,
          },
          {
            header: "主体id",
            dataIndex: "topic",
            render: function (data, table, row) {
              var name = row.placeToken === "07c6d1dc5912405580584c2d02952036" ? '教练':  '驾校';
              let html = `<p>${name}: ${data}</p>`;
              return  html
            },
            width: 150
          },
          {
            header: "主体名称",
            dataIndex: "topicName",
            width: 150,
          },
          {
            header: "主体异常新增",
            dataIndex: "abnormal",
            render:  function (data, table, row) {
              var bool = data ? '是': '否';
              return (
                '<div style="cursor: pointer;" class="userinfo" data-userId="' +
                '">' +
                "<span style=color:" + (data ? 'red' : '#33333')+ '>' +
                bool +
                "</div>"
              );
            },
            width: 150,
          },
          // {
          //   header: "主题信息",
          //   dataIndex: "placeName",
          //   render: function (data, table, row) {
          //     return row.placeName + "<br/>" + "<br/>" + row.topic;
          //   },
          //   // render: function (data, table, row) {
          //   //     return "<a>" + row.placeName + "</a>"
          //   // },
          //   // click: function (table, row, lineData) {
          //   //     Widgets.dialog.html('主题信息id', "<span>" + lineData.topic + "</span>", {
          //   //         width: 300
          //   //     })
          //   // }
          // },
          {
            header: "用户",
            dataIndex: "author",
            render: function (data, table, row) {
              if(!data) {
                return ''
              }
              return (
                '<div style="cursor: pointer;" class="userinfo" data-userId="' +
                data.mucangId +
                '">' +
                '<img src="' +
                data.avatar +
                '" style="max-width:50px; max-height:50px;border-radius: 25px;">' +
                "<br/>" +
                "<span style=color:" + (row.dianpingRed ? 'red' : '#33333')+ '>' +
                data.nickname +
                "</span>" +
                "</div>"
              );
            },
          },
          {
            header: "最新可信度",
            width: 100,
            dataIndex: "credibility",
            render: (data, arr, lineData) =>
              data === 0
                ? (lineData.registerSameDay ? `未知：${lineData.registerSameDay}` : '未知')
                : data === 1
                ? "低"
                : data === 2
                ? "高"
                : "数据暂未同步",
          },
          {
            header: "可信度更新时间",
            width: 100,
            dataIndex: "credibilityUpdateTime",
            render: (data, arr, lineData) =>{
              if(data) {
                return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
              }
            }
          },
          {
            header: "最初可信度",
            width: 100,
            dataIndex: "originalCredibility",
            render: (data, arr, lineData) =>{
              if(!lineData.credibilityUpdateTime){
                return ''
              } else {
                switch(data){
                  case 1: {
                    return '低';
                  }
                  case 2: {
                    return '高';
                  }
                  default: {
                    return '未知';
                  }
                }
              }
            }
          },
          {
            header: "进审原因",
            dataIndex: "reason",
            width: 90,
            render: function (data) {
              return "<div style='white-space: pre-line;'>" + data + "</div>";
            },
          },
          {
            header: "学员评分",
            dataIndex: "score",
            width: 90,
          },
          {
            header: "内容",
            dataIndex: "originalContent",
            width: 500,
            render: function (data, arrData, lineData, index) {
              const title = lineData.originalContent ? lineData.originalContent : lineData.content;
              return '<div style="max-width: 450px;">' + regBrText(title) + "</div>";
            },
          },
          {
            header: "核验状态",
            dataIndex: "manualCkStatus",
            render: function (data, arrData, lineData, index) {
              switch(lineData.manualCkStatus){
                case 0: {
                  return '未核验';
                }
                case 1: {
                  return '核验申请中';
                }
                case 2: {
                  return '核验存在';
                }
                case 3: {
                  return '核验不存在';
                }
                default: {
                  return '未知';
                }
              }
            },
            width: 150
          },
          {
            header: "图片",
            dataIndex: "imageCount",
            render: function (data, arrData, lineData, index) {
              return lineData.imageCount > 0
                ? "<a>" + lineData.imageCount + "图</a>"
                : "";
            },
            click: function (table, row, lineData) {
              var str = "";
              var count = 0;
              for (var i = 0; i < lineData.imageList.length; i++) {
                count++;
                str +=
                  '<img style="" src="' +
                  lineData.imageList[i].url +
                  '!max800" width="300" height="300">' +
                  "第" +
                  count +
                  "张";
              }
              Widgets.dialog.html(+lineData.imageCount + "图", str, {
                width: 800,
              });
            },
            width: 150,
          },
          {
            header: '点评异常属性',
            dataIndex: 'score',
            render: function (data, allData, lineData) {
              var costTime = lineData.costTime && formatDuring(lineData.costTime);
              var provinceName = lineData.provinceName + lineData.cityName
              let html = `<p>点评用时: ${costTime}</p>
                  <p>APP版本:${lineData.version}</p>
                  <p>IP属地:${lineData.ipLocation}</p>
                  <p>驾校省份:${provinceName}</p>
                `;
              return html;
            },
            width: 300
          },
          // {
          //   header: "地点",
          //   dataIndex: "location",
          //   render: function (data, table, row) {
          //     if (row.address) {
          //       return "<a>" + row.location + "</a>";
          //     } else {
          //       return row.location;
          //     }
          //   },
          //   click: function (table, row, lineData) {
          //     Widgets.dialog.html(
          //       "详细地址",
          //       "<span>" + lineData.address + "</span>",
          //       {
          //         width: 300,
          //       }
          //     );
          //   },
          // },
          // {
          //   header: "点评用时",
          //   dataIndex: "costTime",
          //   render: function (data) {
          //     return data && formatDuring(data)
          //   }
          // },
          // {
          //   header: "APP版本",
          //   dataIndex: "version",
          // },
          // {
          //   header: "ip属地",
          //   dataIndex: "ipLocation",
          // },
          // {
          //   header: "驾校省份",
          //   dataIndex: "provinceName",
          //   render: function (data, table, row) {
          //     if(data && row && row.cityName) {
          //       return data + row.cityName
          //     }
          //   }
          // },
          // {
          //     header: '地址',
          //     dataIndex: 'address'
          // },
          // {
          //     header: '状态',
          //     dataIndex: 'status',
          //     width: 70,
          //     render: function (data) {
          //         var val = '';
          //         $.each(reviewStatus, function (i, obj) {
          //             if (data == obj.key) {
          //                 val = obj.value;
          //             }
          //         });
          //         return val;
          //     }
          // },
          {
            header: "创建时间",
            dataIndex: "createTime",
            render: function (data) {
              return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
            },
          },
        ],
      },
      ["dianping!dianping/data/listReview?reason=策略&placeToken=" + searchTokenStore[0].key],
      panel,
      function (ele, dataList, item) {
        getTableList = [];
        let list = dataList.data;
        getTableList = list;
        let checkbox = item("table-selector");
        let checkboxHeader = item("table-header-selector");
        $(checkbox).css({ width: "28px", height: "26px" });
        $(checkboxHeader).css({ width: "28px", height: "26px" });
        $(panel).scrollTop(scrollTop2);
        $(".allCheck").on("click", function () {
          $(".oCheck").click();
        });

        $(".userinfo").on("click", function () {
          $(this).parent().parent().find("[title=查看用户]").click();
        });
      }
    ).render();
  };
 // 查看待审列表待审列表
  var pendingTriaList = function(table, row, lineData) {
    getTableList = []
    Widgets.dialog.html(lineData.topicName || '', {
      height: 600,
      width: '80%',
      done: 'show',
  }).done(function (dialog) {
      Table({
        buttons: {
          top: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
            {
              name: "审核通过",
              class: "primary",
              // click: letPass
              click: function (table, dom, config, arr) {
                letPass(table, arr);
              },
            },
            {
              name: "审核失败",
              class: "warning",
              // click: letFail
              click: function (table, dom, config, arr) {
                letFail(table, arr);
              },
            },
          ],
          bottom: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
            {
              name: "审核通过",
              class: "primary",
              // click: letPass
              click: function (table, dom, config, arr) {
                letPass(table, arr);
              },
            },
            {
              name: "审核失败",
              class: "warning",
              // click: letFail
              click: function (table, dom, config, arr) {
                letFail(table, arr);
              },
            },
          ],
        },
        selector: {
          dataIndex: "id",
        },
        columns: [
          {
            header: "点评id",
            dataIndex: "id",
            width: 180,
          },
          {
            header: "主体id",
            dataIndex: "topic",
            render: function (data, table, row) {
              var name = row.placeToken === "07c6d1dc5912405580584c2d02952036" ? '教练':  '驾校';
              let html = `<p>${name}: ${data}</p>`;
              return  html
            },
            width: 150,
          },
          {
            header: "主体名称",
            dataIndex: "topicName",
            width: 150,
          },
          {
            header: "主体异常新增",
            dataIndex: "abnormal",
            render:  function (data, table, row) {
              var bool = data ? '是': '否';
              var color = data ? 'red' : '#33333';
              let html = `<p style=color:"${color}">${bool}</p>`;
              return html
            },
            width: 150,
          },
          {
            header: "用户",
            dataIndex: "author",
            render: function (data, table, row) {
              if(!data) {
                return ''
              }
              return (
                '<div style="cursor: pointer;" class="userinfo" data-userId="' +
                data.mucangId +
                '">' +
                '<img src="' +
                data.avatar +
                '" style="max-width:50px; max-height:50px;border-radius: 25px;">' +
                "<br/>" +
                "<span style=color:" + (row.dianpingRed ? 'red' : '#33333')+ '>' +
                data.nickname +
                "</span>" +
                "</div>"
              );
            },
          },
          {
            header: "最新可信度",
            width: 100,
            dataIndex: "credibility",
            render: (data, arr, lineData) =>
              data === 0
                ? (lineData.registerSameDay ? `未知：${lineData.registerSameDay}` : '未知')
                : data === 1
                ? "低"
                : data === 2
                ? "高"
                : "数据暂未同步",
          },
          {
            header: "可信度更新时间",
            width: 100,
            dataIndex: "credibilityUpdateTime",
            render: (data, arr, lineData) =>{
              if(data) {
                return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
              }
            }
          },
          {
            header: "最初可信度",
            width: 100,
            dataIndex: "originalCredibility",
            render: (data, arr, lineData) =>{
              if(!lineData.credibilityUpdateTime){
                return ''
              } else {
                switch(data){
                  case 1: {
                    return '低';
                  }
                  case 2: {
                    return '高';
                  }
                  default: {
                    return '未知';
                  }
                }
              }
            }
          },
          {
            header: "进审原因",
            dataIndex: "reason",
            width: 90,
            render: function (data) {
              return "<div style='white-space: pre-line;'>" + data + "</div>";
            },
          },
          {
            header: "内容",
            dataIndex: "content",
            width: 500,
            render: function (data) {
              return '<div style="max-width: 450px;">' + regBrText(data) + "</div>";
            },
          },
          {
            header: "图片",
            dataIndex: "imageCount",
            render: function (data, arrData, lineData, index) {
              return lineData.imageCount > 0
                ? "<a>" + lineData.imageCount + "图</a>"
                : "";
            },
            click: function (table, row, lineData) {
              Plugin('dianping!image-gallery', {
                imageList: lineData.imageList,
                dianpingId: lineData.id,
                showEditButton: true,
                onSave: function () {
                  table.render();
                }
              }).render();
            },
            width: 150,
          },
          {
            header: '点评异常属性',
            dataIndex: 'score',
            render: function (data, allData, lineData) {
              var costTime = lineData.costTime && formatDuring(lineData.costTime);
              var provinceName = lineData.provinceName + lineData.cityName
              let html = `<p>点评用时: ${costTime}</p>
                  <p>APP版本:${lineData.version}</p>
                  <p>IP属地:${lineData.ipLocation}</p>
                  <p>驾校省份:${provinceName}</p>
                `;
              return html;
            },
            width: 300
          },
          // {
          //   header: "地点",
          //   dataIndex: "location",
          //   render: function (data, table, row) {
          //     if (row.address) {
          //       return "<a>" + row.location + "</a>";
          //     } else {
          //       return row.location;
          //     }
          //   },
          //   click: function (table, row, lineData) {
          //     Widgets.dialog.html(
          //       "详细地址",
          //       "<span>" + lineData.address + "</span>",
          //       {
          //         width: 300,
          //       }
          //     );
          //   },
          // },
          // {
          //   header: "点评用时",
          //   dataIndex: "costTime",
          //   render: function (data) {
          //     return data && formatDuring(data)
          //   }
          // },
          // {
          //   header: "APP版本",
          //   dataIndex: "version",
          // },
          // {
          //   header: "ip属地",
          //   dataIndex: "ipLocation",
          // },
          // {
          //   header: "驾校省份",
          //   dataIndex: "provinceName",
          //   render: function (data, table, row) {
          //     if(data && row && row.cityName) {
          //       return data + row.cityName
          //     }
          //   }
          // },
          // {
          //     header: '地址',
          //     dataIndex: 'address'
          // },
          // {
          //     header: '状态',
          //     dataIndex: 'status',
          //     width: 70,
          //     render: function (data) {
          //         var val = '';
          //         $.each(reviewStatus, function (i, obj) {
          //             if (data == obj.key) {
          //                 val = obj.value;
          //             }
          //         });
          //         return val;
          //     }
          // },
          {
            header: "创建时间",
            dataIndex: "createTime",
            render: function (data) {
              return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
            },
            width: 280,
          },
        ],
      }, ['dianping!dianping/data/listReview?deleted=false' + '&topic=' + (lineData.topic || '') + '&placeToken=' + lineData.placeToken || '' ], dialog.body, function (ele, dataList, item) {
        let list = dataList.data;
        getTableList = list;
        let checkbox = item("table-selector");
        let checkboxHeader = item("table-header-selector");
        $(checkbox).css({ width: "28px", height: "26px" });
        $(checkboxHeader).css({ width: "28px", height: "26px" });
        dialog.body.scrollTop(scrollTop2);
        $(".allCheck").on("click", function () {
          $(".oCheck").click();
        });

        $(".userinfo").on("click", function () {
          $(this).parent().parent().find("[title=查看用户]").click();
        });

      }).render();
  });
  };
  return {
    list: list,
    listPending: listPending,
  };
});
