'use strict';

define(['simple!core/template', 'simple!core/widgets', 'simple!core/plugin', 'simple!core/ajax'], function (Template, Widgets, Plugin, Ajax) {
    // 图片上传
    const imageUpload = function (file, appSpaceId = '3a9b06083a9b085239c8') {
        return new Promise((resolve, reject) => {
            var formdata = new FormData();
            formdata.append('files', file);
            Ajax.request(`https://cyclops.mucang.cn/api/admin/upload-file/upload.htm?appSpaceId=${appSpaceId}`, {
                data: formdata,
                type: 'post',
                contentType: false,
                processData: false,
                success: function (data) {
                    let itemList = data?.itemList || []
                    const imageData = itemList.filter(item => {
                        return item.contentType.indexOf('image') > -1;
                    });
                    if (imageData?.length) {
                        resolve(imageData)
                    } else {
                        reject()
                    }
                },
                error: function (data) {
                    reject()
                }
            })
        })
    }


    var ImageGallery = function (plugin, success) {
        this.plugin = plugin;
        this.config = plugin.config;
        this.target = $('[data-plugin=' + plugin.id + ']');

        this.config.imageList.forEach(item => {
            item.previewUrl = item.url.indexOf('!') >= 0 ? item.url : (item.url + '!max800');
        })

        this.render();
        success(this);
    };

    ImageGallery.prototype = {
        render: function () {
            var me = this;
            var imageList = me.config.imageList || []
            var showEditButton = me.config.showEditButton || false

            Template(me.plugin.path + '/template/main/index', null, null, {
                imageList: imageList,
                showEditButton: showEditButton
            }).render().done(function (obj, dom) {
                var dialogButtons = [];
                if (showEditButton) {
                    dialogButtons.push({
                        name: '确认修改',
                        xtype: 'success',
                        click: function () {
                            const that = this

                            Simple.Store(['dianping!dianping/data/maskImageSaveStore'])
                                .save([
                                    {
                                        aliases: 'ret',
                                        params: {
                                            dpId: me.config.dianpingId,
                                            imageMaskDTOList: JSON.stringify(imageList
                                                .filter(item => item.isMosaic)
                                                .map(item => ({
                                                    dpImgId: item.id,
                                                    rawImgUrl: item.url,
                                                    maskImgEncodeData: item.encodedData
                                                })))
                                        }
                                    }
                                ])
                                .done(function (store, un, config, res) {
                                    that.close();
                                    Widgets.dialog.alert('保存成功')
                                    if (me.config.onSave && typeof me.config.onSave === 'function') {
                                        me.config.onSave();
                                    }
                                })
                                .fail(function (res) {
                                    Widgets.dialog.alert(res.message);
                                })
                        }
                    });
                }

                Widgets.dialog.html("共" + imageList.length + "张图片", dom, {
                    width: 640,
                    buttons: dialogButtons
                }).done(function (dialog) {
                    if (showEditButton) {
                        dialog.body.find('.edit-image-btn').on('click', function () {
                            var index = $(this).data('index');

                            me.editImage(index);
                        })
                    }
                })
            });
        },

        editImage: function (index) {
            var me = this;
            var imageList = me.config.imageList || []
            var imageUrl = imageList[index].previewUrl
            console.log('开始编辑图片，imageUrl:', index, imageUrl);

            Plugin('dianping!image-mosaic', {
                image: {
                    url: imageUrl
                },
                onSave: function (file) {
                    console.log('图片编辑完成，保存数据:', file);

                    // 获取当前图片容器和编辑按钮
                    var $imageItem = $('.image-gallery-container .image-item').eq(index);
                    var $editBtn = $imageItem.find('.edit-image-btn');

                    // 显示loading状态
                    me.showImageLoading($imageItem, $editBtn);

                    imageUpload(file).then(list => {
                        const img = list[0];
                        imageList[index].previewUrl = img.previewUrl;
                        imageList[index].encodedData = img.encodedData;
                        imageList[index].isMosaic = true;

                        // 更新DOM中的图片
                        $imageItem.find('img').attr('src', imageList[index].previewUrl);

                        // 隐藏loading状态
                        me.hideImageLoading($imageItem, $editBtn);
                    }).catch(error => {
                        console.error('图片上传失败:', error);

                        // 隐藏loading状态
                        me.hideImageLoading($imageItem, $editBtn);

                        Widgets.dialog.alert('图片上传失败，请重试');
                    });
                }
            }).render();
        },

        showImageLoading: function ($imageItem, $editBtn) {
            // 禁用编辑按钮
            $editBtn.prop('disabled', true).text('上传中...');

            // 添加loading遮罩层
            var $loading = $('<div class="image-upload-loading">' +
                '<div class="loading-spinner"></div>' +
                '<div class="loading-text">图片上传中...</div>' +
                '</div>');

            $imageItem.css('position', 'relative').append($loading);
        },

        hideImageLoading: function ($imageItem, $editBtn) {
            // 启用编辑按钮
            $editBtn.prop('disabled', false).text('编辑');

            // 移除loading遮罩层
            $imageItem.find('.image-upload-loading').remove();
        }
    };

    ImageGallery.prototype.constructor = ImageGallery;

    return function (plugin, success) {
        return new ImageGallery(plugin, success);
    };
});
