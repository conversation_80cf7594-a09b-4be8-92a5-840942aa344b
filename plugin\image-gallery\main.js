'use strict';

define(['simple!core/template', 'simple!core/widgets', 'simple!core/plugin', 'simple!core/ajax'], function (Template, Widgets, Plugin, Ajax) {
    // 图片上传
    const imageUpload = function (file, appSpaceId = '958cf623958c23bc78b6') {
        return new Promise((resolve, reject) => {
            var formdata = new FormData();
            formdata.append('files', file);
            Ajax.request(`https://cyclops.mucang.cn/api/admin/upload-file/upload.htm?appSpaceId=${appSpaceId}`, {
                data: formdata,
                type: 'post',
                contentType: false,
                processData: false,
                success: function (data) {
                    let itemList = data?.itemList || []
                    const imageData = itemList.filter(item => {
                        return item.contentType.indexOf('image') > -1;
                    });
                    if (imageData?.length) {
                        resolve(imageData)
                    } else {
                        reject()
                    }
                },
                error: function (data) {
                    reject()
                }
            })
        })
    }


    var ImageGallery = function (plugin, success) {
        this.plugin = plugin;
        this.config = plugin.config;
        this.target = $('[data-plugin=' + plugin.id + ']');

        this.config.imageList.forEach(item => {
            item.previewUrl = item.url + '!max800';
        })

        this.render();
        success(this);
    };

    ImageGallery.prototype = {
        render: function () {
            var me = this;
            var imageList = me.config.imageList || []
            var showEditButton = me.config.showEditButton || false

            Template(me.plugin.path + '/template/main/index', null, null, {
                imageList: imageList,
                showEditButton: showEditButton
            }).render().done(function (obj, dom) {
                var dialogButtons = [];
                if (showEditButton) {
                    dialogButtons.push({
                        name: '确认修改',
                        xtype: 'success',
                        click: function () {
                            const savePromises = [];
                            imageList.forEach(item => {
                                const promise = new Promise((resolve, reject) => {
                                    Simple.Store2('dianping!dianping/data/maskImageSaveStore', {
                                        dpImgId: item.id,
                                        rawImgUrl: item.url,
                                        maskImgUrl: item.encodedData
                                    }, 'save').then(function () {
                                        resolve();
                                    }, function (data) {
                                        Widgets.dialog.alert(data.message);
                                        reject(data);
                                    });
                                });
                                savePromises.push(promise);
                            })

                            Promise.all(savePromises).then(() => {
                                this.close();
                                Widgets.dialog.alert('保存成功')
                                if (me.config.onSave && typeof me.config.onSave === 'function') {
                                    me.config.onSave();
                                }
                            }).catch(err => {
                                console.error('保存过程中出现错误:', err);
                            });
                        }
                    });
                }

                Widgets.dialog.html("共" + imageList.length + "张图片", dom, {
                    width: 800,
                    buttons: dialogButtons
                }).done(function (dialog) {
                    if (showEditButton) {
                        dialog.body.find('.edit-image-btn').on('click', function () {
                            var index = $(this).data('index');

                            me.editImage(index);
                        })
                    }
                })
            });
        },

        editImage: function (index) {
            var me = this;
            var imageList = me.config.imageList || []
            var imageUrl = imageList[index].url
            console.log('开始编辑图片，imageUrl:', index, imageUrl);

            Plugin('dianping!image-mosaic', {
                image: {
                    url: imageUrl + '!max800'
                },
                onSave: function (file) {
                    console.log('图片编辑完成，保存数据:', file);
                    imageUpload(file).then(list => {
                        const img = list[0];
                        imageList[index].previewUrl = img.previewUrl;
                        imageList[index].encodedData = img.encodedData;
                        imageList[index].isMosaic = true;
                        // 更新DOM中的图片
                        $('.image-gallery-container .image-item').eq(index).find('img').attr('src', imageList[index].previewUrl);
                    })
                }
            }).render();
        }
    };

    ImageGallery.prototype.constructor = ImageGallery;

    return function (plugin, success) {
        return new ImageGallery(plugin, success);
    };
});
