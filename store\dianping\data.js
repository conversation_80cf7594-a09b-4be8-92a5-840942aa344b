﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {
  var list = {
    load: {
      url: "dianping://api/admin/dianping/list.htm",
      type: "get",
      format: function (data) {
        data.map(function (item, index) {
          if (item.content && (item.content.search(/<script.*>/gi) > -1)) {
            item.content = item.content.replace(/<script.*>?/gi, "");
          }

          if (item.content && (item.content.search(/<\/script>/gi) > -1)) {
            item.content = item.content.replace(/<\/script>/gi, "");
          }

          return item;
        });
        return data;
      },
    },
  };

  var del = {
    primary: "id",
    save: {
      url: "dianping://api/admin/dianping/delete.htm",
      type: "post",
    },
  };
  var delReply = {
    save: {
      url: "dianping://api/admin/dianping/delete-jiaxiao-reply.htm",
      type: "post",
    },
  };
  var delProdu = {
    save: {
      url: "dianping://api/admin/dianping/delete-jiaxiao-append-dianping.htm",
      type: "post",
    },
  };

  var insert = {
    save: {
      url: "dianping://api/admin/dianping/create.htm",
      type: "post",
    },
  };

  var update = {
    primary: "id",
    save: {
      url: "dianping://api/admin/dianping/update.htm",
      type: "post",
    },
  };

  var view = {
    primary: "id",
    load: {
      url: "dianping://api/admin/dianping/view.htm",
      type: "get",
    },
  };

  var audit = {
    primary: "id",
    save: {
      url: "dianping://api/admin/dianping/audit.htm",
      type: "post",
    },
  };

  var recover = {
    primary: "id",
    save: {
      url: "dianping://api/admin/dianping/recover.htm",
      type: "post",
    },
  };

  var addJinghua = {
    primary: "id",
    save: {
      url: "dianping://api/admin/dianping/add-jinghua.htm",
      type: "post",
    },
  };

  var removeJinghua = {
    primary: "id",
    save: {
      url: "dianping://api/admin/dianping/remove-jinghua.htm",
      type: "post",
    },
  };

  var listFromDb = {
    load: {
      url: "dianping://api/admin/dianping/list-from-db.htm",
      type: "get",
    },
  };

  //http://dianping.v2.kakamobi.com/api/admin/dianping/list-review.htm
  var listReview = {
    load: {
      url: "dianping://api/admin/dianping/list-review.htm",
      type: "get",
      format: function (data) {
        data.map(function (item, index) {
          if (item.content && (item.content.search(/<script.*>/gi) > -1)) {
            item.content = item.content.replace(/<script.*>?/gi, "");
          }

          if (item.content && (item.content.search(/<\/script>/gi) > -1)) {
            item.content = item.content.replace(/<\/script>/gi, "");
          }

          return item;
        });

        return data;
      },
    },
  };
  var batchDelete = {
    primary: "id",
    save: {
      url: "dianping://api/admin/dianping/delete-batch.htm",
      type: "post",
    },
    //delete-batch.htm
  };

  var delAndForbid = {
    primary: "id",
    save: {
      url: "dianping://api/admin/dianping/delete-and-forbidden.htm",
      type: "post",
    },
  };

  var createAsRobot = {
    save: {
      url: "dianping://api/admin/dianping/create-as-robot.htm",
      type: "post",
    },
  };

  var virtualNumber = {
    load: {
      url: "dianping://api/admin/tools/kefu-virtual-number.htm",
      type: "get",
    },
  };

  var applyToCheckWithMsgStore = {
    primary: "id",
    save: {
      url: "dianping://api/admin/dianping-review/applyToCheckWithMsg.htm",
      type: "get",
    },
  };

  // 发起OA申请隐藏点评
  var applyHiddenStore = {
    primary: "id",
    save: {
      url: "dianping://api/admin/dianping-oa/apply-hidden.htm",
      type: "post",
    },
  };

  // 打码评论图片后再保存的接口
  var maskImageSaveStore = {
    save: {
      url: "dianping://api/admin/dianping-image/mask-image-save.htm",
      type: "post",
    },
  };

  return {
    list: list,
    delete: del,
    delReply: delReply,
    delProdu,
    update: update,
    insert: insert,
    view: view,
    audit: audit,
    recover: recover,
    addJinghua: addJinghua,
    removeJinghua: removeJinghua,
    listFromDb: listFromDb,
    listReview: listReview,
    batchDelete: batchDelete,
    delAndForbid: delAndForbid,
    createAsRobot: createAsRobot,
    virtualNumber: virtualNumber,
    applyToCheckWithMsgStore: applyToCheckWithMsgStore,
    applyHiddenStore: applyHiddenStore,
    maskImageSaveStore: maskImageSaveStore
  };
});
