# 图片画廊组件 (Image Gallery)

## 功能说明

图片画廊组件用于展示图片列表，支持图片编辑功能的开关控制。

## 配置参数

### 基础配置

- `imageList` (Array): 图片列表数组，每个图片对象包含以下属性：
  - `id`: 图片ID
  - `url`: 图片URL
  - `previewUrl`: 预览图片URL（可选，会自动生成）

### 编辑功能配置

- `showEditButton` (Boolean): 是否显示编辑按钮，默认为 `false`
  - `true`: 显示编辑按钮和确认修改按钮
  - `false`: 只显示图片，不显示任何编辑功能

### 回调函数

- `onSave` (Function): 保存完成后的回调函数（可选）

## 使用示例

### 基础使用（只查看图片）

```javascript
Plugin('dianping!image-gallery', {
    imageList: [
        {
            id: '1',
            url: 'https://example.com/image1.jpg'
        },
        {
            id: '2', 
            url: 'https://example.com/image2.jpg'
        }
    ]
}).render();
```

### 开启编辑功能

```javascript
Plugin('dianping!image-gallery', {
    imageList: [
        {
            id: '1',
            url: 'https://example.com/image1.jpg'
        },
        {
            id: '2',
            url: 'https://example.com/image2.jpg'
        }
    ],
    showEditButton: true,  // 开启编辑功能
    onSave: function() {
        console.log('图片保存完成');
        // 执行保存后的操作
    }
}).render();
```

## 功能特性

1. **条件显示编辑按钮**: 只有当 `showEditButton` 为 `true` 时才显示编辑按钮
2. **条件显示确认修改按钮**: 只有当开启编辑功能时，对话框才会显示"确认修改"按钮
3. **图片编辑**: 点击编辑按钮可以调用图片马赛克编辑功能
4. **批量保存**: 支持批量保存所有编辑过的图片
5. **回调支持**: 保存完成后可以执行自定义回调函数

## 注意事项

- 当 `showEditButton` 为 `false` 或未设置时，组件将以只读模式运行
- 编辑功能依赖于 `dianping!image-mosaic` 插件
- 图片保存功能依赖于 `dianping/data/maskImageSaveStore` 数据存储
